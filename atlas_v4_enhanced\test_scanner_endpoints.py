#!/usr/bin/env python3
"""
Test script to verify A.T.L.A.S. scanner API endpoints are working correctly
"""

import asyncio
import aiohttp
import json
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:5001"

async def test_endpoint(session, endpoint, method='GET', data=None):
    """Test a specific API endpoint"""
    try:
        url = f"{BASE_URL}{endpoint}"
        
        if method == 'GET':
            async with session.get(url) as response:
                result = await response.json()
                return response.status, result
        elif method == 'POST':
            async with session.post(url, json=data) as response:
                result = await response.json()
                return response.status, result
                
    except Exception as e:
        logger.error(f"Error testing {endpoint}: {e}")
        return None, {"error": str(e)}

async def main():
    """Main test function"""
    print("🔍 Testing A.T.L.A.S. Scanner API Endpoints")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        
        # Test 1: Health check
        print("\n1️⃣ Testing Health Check...")
        status, result = await test_endpoint(session, "/api/v1/health")
        if status == 200:
            print("✅ Health check passed")
        else:
            print(f"❌ Health check failed: {status} - {result}")
        
        # Test 2: Lee Method signals endpoint
        print("\n2️⃣ Testing Lee Method Signals Endpoint...")
        status, result = await test_endpoint(session, "/api/v1/lee_method/signals")
        if status == 200:
            if result.get('success'):
                signals = result.get('signals', [])
                print(f"✅ Signals endpoint working - {len(signals)} signals found")
                if signals:
                    print(f"   Sample signal: {signals[0].get('symbol', 'N/A')}")
            else:
                print(f"⚠️ Signals endpoint returned success=False: {result}")
        else:
            print(f"❌ Signals endpoint failed: {status} - {result}")
        
        # Test 3: Lee Method stats endpoint
        print("\n3️⃣ Testing Lee Method Stats Endpoint...")
        status, result = await test_endpoint(session, "/api/v1/lee_method/stats")
        if status == 200:
            if result.get('success'):
                stats = result.get('stats', {})
                print(f"✅ Stats endpoint working")
                print(f"   Active signals: {stats.get('active_signals', 'N/A')}")
                print(f"   Pattern accuracy: {stats.get('pattern_accuracy', 'N/A')}")
                print(f"   Scans today: {stats.get('scans_today', 'N/A')}")
            else:
                print(f"⚠️ Stats endpoint returned success=False: {result}")
        else:
            print(f"❌ Stats endpoint failed: {status} - {result}")
        
        # Test 4: Chat endpoint with scanner query
        print("\n4️⃣ Testing Chat Endpoint with Scanner Query...")
        chat_data = {
            "message": "scan market for lee method patterns",
            "session_id": "test_session"
        }
        status, result = await test_endpoint(session, "/api/v1/chat", method='POST', data=chat_data)
        if status == 200:
            response_text = result.get('response', '')
            if 'Lee Method' in response_text or 'scan' in response_text.lower():
                print("✅ Chat endpoint scanner integration working")
            else:
                print(f"⚠️ Chat endpoint response unclear: {response_text[:100]}...")
        else:
            print(f"❌ Chat endpoint failed: {status} - {result}")
        
        # Test 5: Scanner status endpoint
        print("\n5️⃣ Testing Scanner Status Endpoint...")
        status, result = await test_endpoint(session, "/api/v1/scanner/status")
        if status == 200:
            print("✅ Scanner status endpoint working")
            print(f"   Status: {result}")
        else:
            print(f"❌ Scanner status endpoint failed: {status} - {result}")

    print("\n" + "=" * 50)
    print("🏁 Test completed!")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
