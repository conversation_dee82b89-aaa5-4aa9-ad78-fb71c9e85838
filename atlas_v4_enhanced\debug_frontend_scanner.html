<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A.T.L.A.S. Scanner Debug Tool</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .debug-section {
            background-color: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #444;
        }
        .debug-title {
            color: #4CAF50;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .test-button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background-color: #45a049;
        }
        .test-button:disabled {
            background-color: #666;
            cursor: not-allowed;
        }
        .result {
            background-color: #333;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 A.T.L.A.S. Scanner Debug Tool</h1>
        
        <div class="debug-section">
            <div class="debug-title">API Endpoint Tests</div>
            <button class="test-button" onclick="testSignalsEndpoint()">Test Signals Endpoint</button>
            <button class="test-button" onclick="testStatsEndpoint()">Test Stats Endpoint</button>
            <button class="test-button" onclick="testHealthEndpoint()">Test Health Endpoint</button>
            <button class="test-button" onclick="testAllEndpoints()">Test All Endpoints</button>
            <div class="result" id="apiResults">Click a button to test API endpoints...</div>
        </div>

        <div class="debug-section">
            <div class="debug-title">WebSocket Connection Test</div>
            <button class="test-button" onclick="testWebSocket()">Connect WebSocket</button>
            <button class="test-button" onclick="disconnectWebSocket()">Disconnect WebSocket</button>
            <div class="result" id="websocketResults">WebSocket not connected...</div>
        </div>

        <div class="debug-section">
            <div class="debug-title">Frontend Functions Test</div>
            <button class="test-button" onclick="testLoadSignals()">Test loadLeeMethodSignals()</button>
            <button class="test-button" onclick="testLoadStats()">Test loadScannerStats()</button>
            <button class="test-button" onclick="testUpdateDisplay()">Test updateScannerDisplay()</button>
            <div class="result" id="frontendResults">Frontend functions ready for testing...</div>
        </div>

        <div class="debug-section">
            <div class="debug-title">Live Scanner Display</div>
            <div id="scannerResults" style="background-color: #333; padding: 15px; border-radius: 5px;">
                <div class="signal-item">
                    <div class="signal-header">
                        <div class="signal-symbol">Loading signals...</div>
                        <div class="signal-price">--</div>
                    </div>
                    <div class="signal-details">
                        <div class="signal-confidence">Initializing scanner...</div>
                        <div class="signal-strength strength-medium">LOADING</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let websocket = null;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            return `[${timestamp}] ${prefix} ${message}`;
        }

        async function testSignalsEndpoint() {
            const resultDiv = document.getElementById('apiResults');
            resultDiv.innerHTML = log('Testing /api/v1/lee_method/signals...', 'info');
            
            try {
                const response = await fetch('/api/v1/lee_method/signals');
                const data = await response.json();
                
                if (response.ok) {
                    const signalsCount = data.signals ? data.signals.length : 0;
                    resultDiv.innerHTML += '\n' + log(`Signals endpoint OK - ${signalsCount} signals found`, 'success');
                    resultDiv.innerHTML += '\n' + log(`Response: ${JSON.stringify(data, null, 2)}`, 'info');
                } else {
                    resultDiv.innerHTML += '\n' + log(`Signals endpoint failed: ${response.status}`, 'error');
                }
            } catch (error) {
                resultDiv.innerHTML += '\n' + log(`Signals endpoint error: ${error.message}`, 'error');
            }
        }

        async function testStatsEndpoint() {
            const resultDiv = document.getElementById('apiResults');
            resultDiv.innerHTML = log('Testing /api/v1/lee_method/stats...', 'info');
            
            try {
                const response = await fetch('/api/v1/lee_method/stats');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML += '\n' + log('Stats endpoint OK', 'success');
                    resultDiv.innerHTML += '\n' + log(`Response: ${JSON.stringify(data, null, 2)}`, 'info');
                } else {
                    resultDiv.innerHTML += '\n' + log(`Stats endpoint failed: ${response.status}`, 'error');
                }
            } catch (error) {
                resultDiv.innerHTML += '\n' + log(`Stats endpoint error: ${error.message}`, 'error');
            }
        }

        async function testHealthEndpoint() {
            const resultDiv = document.getElementById('apiResults');
            resultDiv.innerHTML = log('Testing /api/v1/health...', 'info');
            
            try {
                const response = await fetch('/api/v1/health');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML += '\n' + log('Health endpoint OK', 'success');
                    resultDiv.innerHTML += '\n' + log(`Response: ${JSON.stringify(data, null, 2)}`, 'info');
                } else {
                    resultDiv.innerHTML += '\n' + log(`Health endpoint failed: ${response.status}`, 'error');
                }
            } catch (error) {
                resultDiv.innerHTML += '\n' + log(`Health endpoint error: ${error.message}`, 'error');
            }
        }

        async function testAllEndpoints() {
            await testHealthEndpoint();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testSignalsEndpoint();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testStatsEndpoint();
        }

        function testWebSocket() {
            const resultDiv = document.getElementById('websocketResults');
            resultDiv.innerHTML = log('Connecting to WebSocket...', 'info');
            
            try {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/ws/scanner`;
                
                websocket = new WebSocket(wsUrl);
                
                websocket.onopen = function(event) {
                    resultDiv.innerHTML += '\n' + log('WebSocket connected successfully', 'success');
                };
                
                websocket.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    resultDiv.innerHTML += '\n' + log(`WebSocket message: ${JSON.stringify(data, null, 2)}`, 'info');
                };
                
                websocket.onerror = function(error) {
                    resultDiv.innerHTML += '\n' + log(`WebSocket error: ${error}`, 'error');
                };
                
                websocket.onclose = function(event) {
                    resultDiv.innerHTML += '\n' + log('WebSocket connection closed', 'warning');
                };
                
            } catch (error) {
                resultDiv.innerHTML += '\n' + log(`WebSocket connection failed: ${error.message}`, 'error');
            }
        }

        function disconnectWebSocket() {
            if (websocket) {
                websocket.close();
                websocket = null;
                document.getElementById('websocketResults').innerHTML += '\n' + log('WebSocket disconnected', 'info');
            }
        }

        async function testLoadSignals() {
            const resultDiv = document.getElementById('frontendResults');
            resultDiv.innerHTML = log('Testing loadLeeMethodSignals() function...', 'info');
            
            try {
                const response = await fetch('/api/v1/lee_method/signals');
                const data = await response.json();
                
                if (data.success && data.signals) {
                    updateScannerDisplay(data.signals);
                    resultDiv.innerHTML += '\n' + log(`loadLeeMethodSignals() test successful - ${data.signals.length} signals`, 'success');
                } else {
                    showNoSignalsMessage();
                    resultDiv.innerHTML += '\n' + log('loadLeeMethodSignals() test - no signals available', 'warning');
                }
            } catch (error) {
                showErrorMessage('Test error');
                resultDiv.innerHTML += '\n' + log(`loadLeeMethodSignals() test failed: ${error.message}`, 'error');
            }
        }

        async function testLoadStats() {
            const resultDiv = document.getElementById('frontendResults');
            resultDiv.innerHTML = log('Testing loadScannerStats() function...', 'info');
            
            try {
                const response = await fetch('/api/v1/lee_method/stats');
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML += '\n' + log('loadScannerStats() test successful', 'success');
                    resultDiv.innerHTML += '\n' + log(`Stats: ${JSON.stringify(data.stats, null, 2)}`, 'info');
                } else {
                    resultDiv.innerHTML += '\n' + log('loadScannerStats() test failed - no success flag', 'error');
                }
            } catch (error) {
                resultDiv.innerHTML += '\n' + log(`loadScannerStats() test failed: ${error.message}`, 'error');
            }
        }

        function testUpdateDisplay() {
            const resultDiv = document.getElementById('frontendResults');
            resultDiv.innerHTML = log('Testing updateScannerDisplay() function...', 'info');
            
            // Test with sample data
            const sampleSignals = [
                {
                    symbol: 'AAPL',
                    price: 150.25,
                    confidence: 0.85,
                    pattern_type: 'Lee Method'
                },
                {
                    symbol: 'MSFT',
                    price: 280.50,
                    confidence: 0.72,
                    pattern_type: 'Lee Method'
                }
            ];
            
            updateScannerDisplay(sampleSignals);
            resultDiv.innerHTML += '\n' + log('updateScannerDisplay() test completed with sample data', 'success');
        }

        // Scanner display functions (simplified versions)
        function updateScannerDisplay(signals) {
            const scannerResults = document.getElementById('scannerResults');
            
            if (!signals || signals.length === 0) {
                showNoSignalsMessage();
                return;
            }
            
            let html = '';
            signals.forEach(signal => {
                const strengthClass = getStrengthClass(signal.confidence);
                const strengthText = getStrengthText(signal.confidence);
                
                html += `
                    <div class="signal-item">
                        <div class="signal-header">
                            <div class="signal-symbol">${signal.symbol}</div>
                            <div class="signal-price">$${signal.price ? signal.price.toFixed(2) : '--'}</div>
                        </div>
                        <div class="signal-details">
                            <div class="signal-confidence">Confidence: ${(signal.confidence * 100).toFixed(1)}%</div>
                            <div class="signal-strength ${strengthClass}">${strengthText}</div>
                        </div>
                    </div>
                `;
            });
            
            scannerResults.innerHTML = html;
        }

        function showNoSignalsMessage() {
            const scannerResults = document.getElementById('scannerResults');
            scannerResults.innerHTML = `
                <div class="signal-item">
                    <div class="signal-header">
                        <div class="signal-symbol">No Active Signals</div>
                        <div class="signal-price">--</div>
                    </div>
                    <div class="signal-details">
                        <div class="signal-confidence">Scanning for Lee Method patterns...</div>
                        <div class="signal-strength strength-low">SCANNING</div>
                    </div>
                </div>
            `;
        }

        function showErrorMessage(message) {
            const scannerResults = document.getElementById('scannerResults');
            scannerResults.innerHTML = `
                <div class="signal-item">
                    <div class="signal-header">
                        <div class="signal-symbol">Scanner Error</div>
                        <div class="signal-price">--</div>
                    </div>
                    <div class="signal-details">
                        <div class="signal-confidence">${message}</div>
                        <div class="signal-strength strength-low">ERROR</div>
                    </div>
                </div>
            `;
        }

        function getStrengthClass(confidence) {
            if (confidence >= 0.8) return 'strength-high';
            if (confidence >= 0.6) return 'strength-medium';
            return 'strength-low';
        }

        function getStrengthText(confidence) {
            if (confidence >= 0.8) return 'HIGH';
            if (confidence >= 0.6) return 'MEDIUM';
            return 'LOW';
        }
    </script>
</body>
</html>
